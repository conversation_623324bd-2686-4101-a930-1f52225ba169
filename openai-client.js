#!/usr/bin/env node

import OpenAI from 'openai';
import readline from 'readline';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import {
  ListToolsResultSchema,
  CallToolResultSchema
} from '@modelcontextprotocol/sdk/types.js';

// 配置 - 请填写你的实际配置
const CONFIG = {
  apiKey: 'sk-kjvycidpbzmzploczfpluttflidxbrtpesownzpayddaenbo',
  baseURL: 'https://api.siliconflow.cn/v1',
  model: 'moonshotai/Kimi-K2-Instruct'
};

class OpenAIClient {
  constructor() {
    this.openai = new OpenAI({
      apiKey: CONFIG.apiKey,
      baseURL: CONFIG.baseURL,
    });
    this.mcpClient = null;
    this.tools = [];
    this.conversationHistory = [];
  }

  async init () {
    try {
      // 创建 MCP 客户端
      this.mcpClient = new Client({
        name: "openai-mcp-client",
        version: "1.0.0",
      }, {
        capabilities: {
          tools: {},
        },
      });

      // 连接到 MCP 服务器
      const transport = new StdioClientTransport({
        command: "node",
        args: ["mcp-server.js"],
      });

      await this.mcpClient.connect(transport);

      // 获取可用工具
      const toolsResult = await this.mcpClient.request(
        { method: "tools/list" },
        ListToolsResultSchema
      );

      // 转换工具格式为 OpenAI 格式
      this.tools = toolsResult.tools.map(tool => ({
        type: "function",
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.inputSchema
        }
      }));

      console.log(`✅ 已连接到 MCP 服务器，加载了 ${this.tools.length} 个工具`);
    } catch (error) {
      console.error('❌ 无法连接到 MCP 服务器:', error.message);
      console.log('请确保 MCP 服务器文件存在: mcp-server.js');
      process.exit(1);
    }
  }

  async callMCPTool (name, args) {
    try {
      const result = await this.mcpClient.request(
        {
          method: "tools/call",
          params: {
            name: name,
            arguments: args
          }
        },
        { type: "object" }
      );

      return result;
    } catch (error) {
      throw new Error(`MCP 工具调用失败: ${error.message}`);
    }
  }

  async chat (userMessage) {
    // 添加用户消息到历史
    this.conversationHistory.push({
      role: 'user',
      content: userMessage
    });

    try {
      // 调用 OpenAI API
      const completion = await this.openai.chat.completions.create({
        model: CONFIG.model,
        messages: [
          ...this.conversationHistory
        ],
        tools: this.tools,
        tool_choice: 'auto',
      });

      const message = completion.choices[0].message;

      // 处理工具调用
      if (message.tool_calls && message.tool_calls.length > 0) {
        // 添加助手消息到历史
        this.conversationHistory.push(message);

        // 执行工具调用
        for (const toolCall of message.tool_calls) {
          const { name, arguments: args } = toolCall.function;
          console.log(`🔧 执行工具: ${name}`);
          const result = await this.callMCPTool(name, JSON.parse(args));

          // 添加工具结果到历史
          this.conversationHistory.push({
            role: 'tool',
            tool_call_id: toolCall.id,
            content: JSON.stringify(result)
          });
        }

        // 再次调用 OpenAI 获取最终响应
        const finalCompletion = await this.openai.chat.completions.create({
          model: CONFIG.model,
          messages: [
            ...this.conversationHistory
          ],
        });

        const finalMessage = finalCompletion.choices[0].message;
        this.conversationHistory.push(finalMessage);
        return finalMessage.content;
      } else {
        // 没有工具调用，直接返回响应
        this.conversationHistory.push(message);
        return message.content;
      }
    } catch (error) {
      console.error('❌ OpenAI API 调用失败:', error.message);
      return '抱歉，我遇到了一些问题。请稍后再试。';
    }
  }

  async interactive () {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    console.log('\n=== OpenAI + MCP TODO 助手 ===');
    console.log('你可以用自然语言管理你的 TODO 列表');
    console.log('例如: "显示所有待办事项", "添加学习 JavaScript", "完成第一个任务"');
    console.log('输入 "quit" 退出\n');

    const askQuestion = () => {
      rl.question('你: ', async (input) => {
        if (input.trim().toLowerCase() === 'quit') {
          console.log('再见! 👋');
          if (this.mcpClient) {
            await this.mcpClient.close();
          }
          rl.close();
          process.exit(0);
        }

        if (input.trim() === '') {
          askQuestion();
          return;
        }

        console.log('🤔 思考中...');
        const response = await this.chat(input);
        console.log(`\n助手: ${response}\n`);

        askQuestion();
      });
    };

    askQuestion();
  }
}

async function main () {
  const client = new OpenAIClient();
  await client.init();
  await client.interactive();
}

main().catch((error) => {
  console.error('客户端启动失败:', error);
  process.exit(1);
});
