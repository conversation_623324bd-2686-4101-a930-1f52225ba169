#!/usr/bin/env node

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { 
  ListToolsResultSchema,
  CallToolResultSchema 
} from '@modelcontextprotocol/sdk/types.js';

async function testMCPClient() {
  console.log('🚀 开始测试 MCP 客户端...');
  
  try {
    // 创建 MCP 客户端
    const mcpClient = new Client({
      name: "test-mcp-client",
      version: "1.0.0",
    }, {
      capabilities: {
        tools: {},
      },
    });

    // 连接到 MCP 服务器
    const transport = new StdioClientTransport({
      command: "node",
      args: ["mcp-server.js"],
    });

    await mcpClient.connect(transport);
    console.log('✅ 已连接到 MCP 服务器');

    // 获取可用工具
    const toolsResult = await mcpClient.request(
      { method: "tools/list" },
      ListToolsResultSchema
    );

    console.log(`📋 可用工具 (${toolsResult.tools.length} 个):`);
    toolsResult.tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description}`);
    });

    // 测试获取 TODO 列表
    console.log('\n🔧 测试获取 TODO 列表...');
    const listResult = await mcpClient.request(
      {
        method: "tools/call",
        params: {
          name: "list_todos",
          arguments: {}
        }
      },
      CallToolResultSchema
    );

    console.log('📝 当前 TODO 列表:');
    console.log(listResult.content[0].text);

    // 测试添加新的 TODO
    console.log('\n🔧 测试添加新的 TODO...');
    const addResult = await mcpClient.request(
      {
        method: "tools/call",
        params: {
          name: "add_todo",
          arguments: { title: "测试 MCP 客户端" }
        }
      },
      CallToolResultSchema
    );

    console.log('✅ 添加结果:');
    console.log(addResult.content[0].text);

    // 再次获取 TODO 列表确认添加成功
    console.log('\n🔧 再次获取 TODO 列表确认...');
    const listResult2 = await mcpClient.request(
      {
        method: "tools/call",
        params: {
          name: "list_todos",
          arguments: {}
        }
      },
      CallToolResultSchema
    );

    console.log('📝 更新后的 TODO 列表:');
    console.log(listResult2.content[0].text);

    // 关闭连接
    await mcpClient.close();
    console.log('\n✅ 测试完成，连接已关闭');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

testMCPClient();
